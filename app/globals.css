@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  
  /* Base colors with light, professional palette */
  --background: oklch(0.99 0.005 240);
  --foreground: oklch(0.15 0.01 240);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.15 0.01 240);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.15 0.01 240);
  
  /* Primary - Soft blue for professional HR system */
  --primary: oklch(0.55 0.15 240);
  --primary-foreground: oklch(0.99 0.005 240);
  
  /* Secondary - Light lavender */
  --secondary: oklch(0.96 0.02 270);
  --secondary-foreground: oklch(0.25 0.02 270);
  
  /* Muted - Very light gray with hint of blue */
  --muted: oklch(0.97 0.01 240);
  --muted-foreground: oklch(0.5 0.01 240);
  
  /* Accent - Soft mint green */
  --accent: oklch(0.95 0.03 150);
  --accent-foreground: oklch(0.2 0.03 150);
  
  /* Destructive - Soft coral red */
  --destructive: oklch(0.6 0.15 15);
  --destructive-foreground: oklch(0.99 0.005 15);
  
  /* Borders and inputs */
  --border: oklch(0.92 0.01 240);
  --input: oklch(0.94 0.01 240);
  --ring: oklch(0.55 0.15 240);
  
  /* Chart colors - Pastel palette */
  --chart-1: oklch(0.7 0.12 200); /* Soft blue */
  --chart-2: oklch(0.7 0.12 150); /* Soft green */
  --chart-3: oklch(0.7 0.12 270); /* Soft purple */
  --chart-4: oklch(0.7 0.12 60);  /* Soft yellow */
  --chart-5: oklch(0.7 0.12 330); /* Soft pink */
  
  /* Sidebar - Clean white with subtle tints */
  --sidebar: oklch(0.995 0.002 240);
  --sidebar-foreground: oklch(0.2 0.01 240);
  --sidebar-primary: oklch(0.55 0.15 240);
  --sidebar-primary-foreground: oklch(0.99 0.005 240);
  --sidebar-accent: oklch(0.96 0.02 240);
  --sidebar-accent-foreground: oklch(0.25 0.02 240);
  --sidebar-border: oklch(0.93 0.01 240);
  --sidebar-ring: oklch(0.55 0.15 240);
}

.dark {
  /* Dark mode with softer, warmer tones */
  --background: oklch(0.12 0.01 240);
  --foreground: oklch(0.95 0.005 240);
  --card: oklch(0.16 0.01 240);
  --card-foreground: oklch(0.95 0.005 240);
  --popover: oklch(0.16 0.01 240);
  --popover-foreground: oklch(0.95 0.005 240);
  
  /* Primary - Brighter blue for dark mode */
  --primary: oklch(0.7 0.18 240);
  --primary-foreground: oklch(0.12 0.01 240);
  
  /* Secondary - Darker lavender */
  --secondary: oklch(0.22 0.02 270);
  --secondary-foreground: oklch(0.9 0.005 270);
  
  /* Muted - Dark gray with blue tint */
  --muted: oklch(0.2 0.01 240);
  --muted-foreground: oklch(0.65 0.01 240);
  
  /* Accent - Darker mint */
  --accent: oklch(0.25 0.03 150);
  --accent-foreground: oklch(0.9 0.005 150);
  
  /* Destructive - Softer red for dark mode */
  --destructive: oklch(0.65 0.18 15);
  --destructive-foreground: oklch(0.95 0.005 15);
  
  /* Borders and inputs */
  --border: oklch(0.25 0.01 240);
  --input: oklch(0.2 0.01 240);
  --ring: oklch(0.7 0.18 240);
  
  /* Chart colors - Darker but still distinguishable */
  --chart-1: oklch(0.6 0.15 200); /* Blue */
  --chart-2: oklch(0.6 0.15 150); /* Green */
  --chart-3: oklch(0.6 0.15 270); /* Purple */
  --chart-4: oklch(0.6 0.15 60);  /* Yellow */
  --chart-5: oklch(0.6 0.15 330); /* Pink */
  
  /* Sidebar - Slightly lighter than background */
  --sidebar: oklch(0.14 0.01 240);
  --sidebar-foreground: oklch(0.9 0.005 240);
  --sidebar-primary: oklch(0.7 0.18 240);
  --sidebar-primary-foreground: oklch(0.12 0.01 240);
  --sidebar-accent: oklch(0.22 0.02 240);
  --sidebar-accent-foreground: oklch(0.9 0.005 240);
  --sidebar-border: oklch(0.25 0.01 240);
  --sidebar-ring: oklch(0.7 0.18 240);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Bento Grid Enhancements */
.bento-grid {
  display: grid;
  gap: 1rem;
  grid-template-columns: repeat(1, 1fr);
  grid-auto-rows: minmax(min-content, auto);
}

@media (min-width: 768px) {
  .bento-grid {
    grid-template-columns: repeat(6, 1fr);
    gap: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .bento-grid {
    grid-template-columns: repeat(12, 1fr);
    gap: 1.5rem;
  }
}

/* Enhanced card hover effects for bento grid */
.bento-grid [class*="col-span"] > * {
  transition: all 0.2s ease-in-out;
}

.bento-grid [class*="col-span"] > *:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px -8px rgba(0, 0, 0, 0.1);
}
