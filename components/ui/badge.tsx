import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"

const badgeVariants = cva(
  "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      variant: {
        default:
          "border-transparent bg-primary text-primary-foreground hover:bg-primary/80",
        secondary:
          "border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",
        destructive:
          "border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",
        outline: "text-foreground",
        // HR specific status badges
        success:
          "border-transparent bg-status-success text-white hover:bg-status-success/80",
        warning:
          "border-transparent bg-status-warning text-white hover:bg-status-warning/80",
        error:
          "border-transparent bg-status-error text-white hover:bg-status-error/80",
        info:
          "border-transparent bg-status-info text-white hover:bg-status-info/80",
        // HR module specific badges
        attendance:
          "border-transparent bg-hr-attendance text-white hover:bg-hr-attendance/80",
        leave:
          "border-transparent bg-hr-leave text-white hover:bg-hr-leave/80",
        payroll:
          "border-transparent bg-hr-payroll text-white hover:bg-hr-payroll/80",
        expense:
          "border-transparent bg-hr-expense text-white hover:bg-hr-expense/80",
        performance:
          "border-transparent bg-hr-performance text-white hover:bg-hr-performance/80",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
)

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badgeVariants> {}

function Badge({ className, variant, ...props }: BadgeProps) {
  return (
    <div className={cn(badgeVariants({ variant }), className)} {...props} />
  )
}

export { Badge, badgeVariants }